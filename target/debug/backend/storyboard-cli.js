#!/usr/bin/env node

// CLI wrapper for the storyboard generator
const StoryboardGenerator = require('./storyboard-generator');
const fs = require('fs');
const path = require('path');

async function main() {
    try {
        // Parse command line arguments
        const args = process.argv.slice(2);
        if (args.length < 2) {
            console.error('Usage: node storyboard-cli.js <config-file> <output-file>');
            process.exit(1);
        }

        const configFile = args[0];
        const outputFile = args[1];

        // Read configuration
        const configData = JSON.parse(fs.readFileSync(configFile, 'utf8'));
        
        // Extract API key and form data
        const apiKey = configData.apiKey;
        const formData = configData.formData;

        if (!apiKey) {
            throw new Error('API key is required');
        }

        // Create generator instance
        const generator = new StoryboardGenerator(apiKey);

        // Progress callback
        const progressCallback = (message) => {
            console.log(`PROGRESS: ${message}`);
        };

        // Generate storyboard
        const result = await generator.generateStoryboard(formData, progressCallback);

        // Write result to output file
        fs.writeFileSync(outputFile, JSON.stringify(result, null, 2));

        console.log('SUCCESS: Storyboard generated successfully');
        process.exit(0);

    } catch (error) {
        console.error('ERROR:', error.message);
        
        // Write error result
        const errorResult = {
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        };
        
        if (process.argv.length >= 4) {
            const outputFile = process.argv[3];
            fs.writeFileSync(outputFile, JSON.stringify(errorResult, null, 2));
        }
        
        process.exit(1);
    }
}

main();
