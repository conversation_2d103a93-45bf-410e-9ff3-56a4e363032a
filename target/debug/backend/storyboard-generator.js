// JavaScript-based Storyboard Generator
// Converts Python PowerPoint generation logic to JavaScript

const PptxGenJS = require('pptxgenjs');
const OpenAI = require('openai');
const axios = require('axios');
const path = require('path');

class StoryboardGenerator {
    constructor(apiKey) {
        this.openai = new OpenAI({ apiKey });
        this.aspectRatios = {
            '16:9': { width: 1.75, value: '16:9' },
            '1:1': { width: 1, value: '1:1' },
            '9:16': { width: 0.57, value: '9:16' },
            // HTML form compatibility
            'WIDESCREEN': { width: 1.75, value: '16:9' },
            'SQUARE': { width: 1, value: '1:1' },
            'VERTICAL': { width: 0.57, value: '9:16' }
        };
        
        this.styles = {
            'sketchy': 'Sketchy graphic style, quick, rough and imprecise marker strokes, not too detailed, with few strokes, white background, black color for lines and a light gray scale for shades.',
            'cartoon': 'Cartoon-like images with vibrant colors, clean lines, friendly and approachable.',
            'realistic': 'Photo-realistic pictures with high detail, natural lighting, professional photography quality.',
            'custom': '' // Will be filled from customStyleDescription
        };
        
        // Extended style descriptions for custom styles
        this.extendedStyles = {
            'watercolor': 'Watercolor painting style with soft, flowing colors and organic textures, gentle brush strokes, artistic paper texture.',
            'oil_painting': 'Oil painting style with rich textures, visible brushstrokes, classical composition, and deep colors.',
            'digital_art': 'Modern digital art with clean lines, vibrant colors, contemporary aesthetics, and polished finish.',
            'pencil_sketch': 'Detailed pencil sketch with fine lines, shading, artistic composition, and graphite texture.',
            'ink_drawing': 'Bold ink drawing with strong contrasts, clean lines, minimal color, and artistic line work.',
            'pastel': 'Soft pastel artwork with gentle colors, smooth transitions, dreamy atmosphere, and artistic texture.',
            'vector_art': 'Clean vector illustration with geometric shapes, flat colors, modern design, and precise lines.',
            'minimalist': 'Minimalist style with simple shapes, limited colors, clean composition, and modern aesthetic.',
            'vintage': 'Vintage illustration style with retro colors, classic design elements, and nostalgic atmosphere.',
            'isometric': 'Isometric 3D style with geometric precision, modern technical aesthetic, and clean perspective.'
        };
    }

    // NOTE: generateStoryboard is implemented later with persona + comprehensive slide. Keep a single definition.

    async generateStoryStructure(formData) {
        // Get the appropriate style description
        let styleDescription = this.styles[formData.baseStyle];
        if (formData.baseStyle === 'custom' && formData.customStyleDescription) {
            // First check if it matches one of our extended styles
            const styleKey = formData.customStyleDescription.toLowerCase().replace(/\s+/g, '_');
            styleDescription = this.extendedStyles[styleKey] || formData.customStyleDescription;
        }
        
        const prompt = `Create a comprehensive customer journey story based on the following requirements:

PROJECT DESCRIPTION: ${formData.description}

REQUIREMENTS:
- Language: ${formData.outputLanguage || 'English'}
- Number of steps: ${formData.nbSteps || 'AI-determined (5-12 steps)'}
- Include emotions: ${formData.includeEmotions ? 'Yes' : 'No'}
- Include challenges: ${formData.includeChallenges ? 'Yes' : 'No'}
- Industry: ${formData.industry || 'General'}
- Business type: ${formData.businessType || 'General'}

${formData.expectations ? `CUSTOMER INSIGHTS: ${formData.expectations}` : ''}

${formData.avoidedTerms && formData.avoidedTerms.length > 0 ? `TERMS TO AVOID: ${formData.avoidedTerms.join(', ')}` : ''}

STORY REQUIREMENTS:
- Create a realistic customer journey from awareness to advocacy
- Include specific persona details (name, age, occupation, personality traits)
- Provide detailed scene descriptions for each step
- Include environmental context and visual details
- Make each step authentic and relatable

Return a JSON structure with:
{
  "story_name": "Customer Journey Title",
  "persona": {
    "name": "Persona Name",
    "age_range": "25-35",
    "occupation": "Job Title",
    "personality_traits": ["trait1", "trait2", "trait3"],
    "background": "Brief background description"
  },
  "story_context": "Overall journey context",
  "scenes": [
    {
      "step_name": "Step Name",
      "step_description": "Detailed description of what happens",
      "scene_description": "Visual scene description for image generation",
      "photo_shooting": "CLOSEUP_SHOT|MEDIUM_SHOT|FULL_SHOT|AMERICAN_SHOT|MEDIUM_CLOSEUP_SHOT",
      "customer_emotion": "Primary emotion" (if emotions enabled),
      "pain_points": ["challenge1", "challenge2"] (if challenges enabled),
      "opportunities": ["opportunity1", "opportunity2"]
    }
  ]
}`;

        const response = await this.openai.chat.completions.create({
            // Chat model now always supplied by main process (menu selection)
            model: formData.chatModel,
            messages: [{ role: 'user', content: prompt }],
            max_tokens: 4000,
            temperature: 0.7
        });

        let storyData;
        try {
            // Try to parse JSON from the response
            const content = response.choices[0].message.content;
            const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/) || content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                storyData = JSON.parse(jsonMatch[1] || jsonMatch[0]);
            } else {
                storyData = JSON.parse(content);
            }
        } catch (parseError) {
            console.error('Error parsing story JSON:', parseError);
            // Fallback to a structured story based on the raw text
            storyData = this.createFallbackStory(response.choices[0].message.content, formData);
        }

        // Ensure stages are generated dynamically (LLM-based) if not provided
        if (storyData.scenes && !storyData.stages) {
            try {
                storyData = await this.ensureStages(storyData, formData);
            } catch (e) {
                console.warn('Dynamic stage generation failed, falling back to heuristic:', e.message);
                storyData = this.convertScenestoStages(storyData);
            }
        }
        return storyData;
    }

    async ensureStages(storyData, formData) {
        if (storyData.stages && storyData.stages.length) return storyData;
        const generated = await this.generateStagesFromScenes(storyData, formData);
        if (generated && generated.length) {
            return { ...storyData, stages: generated };
        }
        return storyData;
    }

    async generateStagesFromScenes(storyData, formData) {
        const scenes = storyData.scenes || [];
        if (!scenes.length) return [];
        // Decide desired number of stages (heuristic 4-6) based on scene count
        const sceneCount = scenes.length;
        let minStages = 4, maxStages = 6;
        if (sceneCount <= 4) { minStages = 2; maxStages = 3; }
        else if (sceneCount <= 6) { minStages = 3; maxStages = 4; }
        else if (sceneCount >= 12) { maxStages = 7; }

        const sceneSummary = scenes.map((s, i) => ({ index: i+1, step_name: s.step_name, step_description: s.step_description })).slice(0, 30); // cap for token safety
        const prompt = `You are an expert in customer journey mapping.
Group the following journey steps into coherent stages.
Return ONLY valid JSON with an array named stages. Each stage must have:
stage_name (short), stage_description (1 sentence), stage_goal (customer primary goal), steps (array of original step_name values in chronological order).
Keep original step ordering overall.
Target number of stages: ${minStages}-${maxStages}.
Steps JSON: ${JSON.stringify(sceneSummary, null, 2)}
Strict JSON schema:
{"stages":[{"stage_name":"","stage_description":"","stage_goal":"","steps":["step name 1", "step name 2"]}]}`;

        try {
            const response = await this.openai.chat.completions.create({
                model: formData.chatModel,
                messages: [{ role: 'user', content: prompt }],
                temperature: 0.3,
                max_tokens: 1200
            });
            const content = response.choices[0].message.content;
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            const parsed = JSON.parse(jsonMatch ? jsonMatch[0] : content);
            const stagesRaw = parsed.stages || [];
            // Map back to full step objects
            const stageObjs = stagesRaw.map(st => {
                const steps = (st.steps || []).map(name => {
                    return scenes.find(s => s.step_name === name) || { step_name: name, step_description: '', scene_description: '', photo_shooting: 'MEDIUM_SHOT' };
                });
                return {
                    stage_name: st.stage_name || 'Stage',
                    stage_description: st.stage_description || '',
                    stage_goal: st.stage_goal || '',
                    steps
                };
            }).filter(st => (st.steps || []).length);
            if (stageObjs.length) return stageObjs;
            return [];
        } catch (err) {
            console.warn('generateStagesFromScenes error:', err.message);
            return [];
        }
    }

    async translateStageNamesIfNeeded(story, formData) {
        try {
            const targetLang = (formData.outputLanguage || 'English').trim();
            if (!targetLang || /^english$/i.test(targetLang)) return story; // No translation needed
            if (!story.stages || story.stages.length === 0) return story;

            const stageNames = story.stages.map(s => s.stage_name || '');
            // Skip if already appears translated (rudimentary check: non-latin chars and target not English)
            if (stageNames.every(n => /[\u00C0-\u024F\u4E00-\u9FFF]/.test(n))) return story;

            const prompt = `Translate the following customer journey stage names into ${targetLang}.
Return ONLY a valid JSON array of translated strings in the same order.
Original JSON: ${JSON.stringify(stageNames)}`;
            const response = await this.openai.chat.completions.create({
                model: formData.chatModel,
                messages: [{ role: 'user', content: prompt }],
                temperature: 0.2,
                max_tokens: 500
            });
            const content = response.choices[0].message.content.trim();
            let translated;
            try {
                const match = content.match(/\[[\s\S]*\]/);
                translated = JSON.parse(match ? match[0] : content);
            } catch (e) {
                return story; // fallback silently
            }
            if (Array.isArray(translated) && translated.length === stageNames.length) {
                story.stages.forEach((stage, i) => { stage.stage_name = translated[i]; });
            }
        } catch (err) {
            console.warn('Stage translation skipped due to error:', err.message);
        }
        return story;
    }

    // Generate a lightweight preview similar to the Streamlit preview (no images, condensed descriptions)
    async generatePreview(formData) {
    let story = await this.generateStoryStructure(formData);
    story = await this.translateStageNamesIfNeeded(story, formData);
    // Stages already ensured in generateStoryStructure
    const staged = story;
        // Normalize persona fields to a preview shape
        const persona = staged.persona || staged.persona_profile || {
            name: 'Customer', age_range: '—', occupation: '—', personality_traits: []
        };
        return {
            story_name: staged.story_name,
            persona: {
                name: persona.name,
                age_range: persona.age_range,
                occupation: persona.occupation,
                traits: (persona.personality_traits || persona.traits || []).slice(0, 3)
            },
            story_context: staged.story_context,
            stages: (staged.stages || []).map(stage => ({
                name: stage.stage_name,
                description: stage.stage_description,
                goal: stage.stage_goal,
                steps: (stage.steps || []).map(step => ({
                    name: step.step_name,
                    description: (step.step_description || '').length > 200 ? (step.step_description || '').slice(0, 200) + '...' : (step.step_description || ''),
                    shot_type: step.photo_shooting || 'MEDIUM_SHOT',
                    emotion: step.customer_emotion || 'N/A'
                }))
            }))
        };
    }

    convertScenestoStages(storyData) {
        // Convert flat scenes array to hierarchical stages structure
        // Group scenes into logical stages based on typical customer journey phases
        const scenes = storyData.scenes || [];
        const numScenes = scenes.length;

        let stages = [];

        if (numScenes <= 4) {
            // Small journey: create meaningful stage names based on content
            const meaningfulStageNames = this.generateMeaningfulStageNames(scenes);
            stages = scenes.map((scene, index) => ({
                stage_name: meaningfulStageNames[index] || `Stage ${index + 1}`,
                stage_description: `${meaningfulStageNames[index] || 'Customer journey stage'} phase`,
                stage_goal: `Complete ${scene.step_name}`,
                steps: [scene]
            }));
        } else if (numScenes <= 6) {
            // Medium journey: group into 3-4 stages with meaningful names
            const stageNames = ['Awareness and Preparation', 'Evaluation and Planning', 'Decision and Purchase', 'Implementation and Experience'];
            const scenesPerStage = Math.ceil(numScenes / 4);

            for (let i = 0; i < 4 && i * scenesPerStage < numScenes; i++) {
                const stageScenes = scenes.slice(i * scenesPerStage, (i + 1) * scenesPerStage);
                if (stageScenes.length > 0) {
                    stages.push({
                        stage_name: stageNames[i],
                        stage_description: `${stageNames[i]} phase of the customer journey`,
                        stage_goal: `Complete ${stageNames[i].toLowerCase()} activities`,
                        steps: stageScenes
                    });
                }
            }
        } else {
            // Large journey: group into 5 stages with meaningful names
            const stageNames = ['Awareness and Research', 'Discovery and Exploration', 'Evaluation and Comparison', 'Purchase and Onboarding', 'Implementation and Advocacy'];
            const scenesPerStage = Math.ceil(numScenes / 5);

            for (let i = 0; i < 5 && i * scenesPerStage < numScenes; i++) {
                const stageScenes = scenes.slice(i * scenesPerStage, (i + 1) * scenesPerStage);
                if (stageScenes.length > 0) {
                    stages.push({
                        stage_name: stageNames[i],
                        stage_description: `${stageNames[i]} phase of the customer journey`,
                        stage_goal: `Complete ${stageNames[i].toLowerCase()} activities`,
                        steps: stageScenes
                    });
                }
            }
        }

        return {
            ...storyData,
            stages: stages,
            // Keep scenes for backward compatibility
            scenes: scenes
        };
    }

    generateMeaningfulStageNames(scenes) {
        // Generate meaningful stage names based on scene content
        const stageNames = [];

        for (let i = 0; i < scenes.length; i++) {
            const scene = scenes[i];
            const stepName = scene.step_name || '';
            const description = scene.step_description || '';

            // Analyze content to generate meaningful names
            if (stepName.toLowerCase().includes('discover') || stepName.toLowerCase().includes('research') || stepName.toLowerCase().includes('aware')) {
                stageNames.push('Awareness and Preparation');
            } else if (stepName.toLowerCase().includes('install') || stepName.toLowerCase().includes('implement') || stepName.toLowerCase().includes('site')) {
                stageNames.push('On-Site Installation');
            } else if (stepName.toLowerCase().includes('complete') || stepName.toLowerCase().includes('success') || stepName.toLowerCase().includes('advocacy')) {
                stageNames.push('Completion and Advocacy');
            } else if (stepName.toLowerCase().includes('evaluat') || stepName.toLowerCase().includes('compar') || stepName.toLowerCase().includes('decision')) {
                stageNames.push('Evaluation and Decision');
            } else if (stepName.toLowerCase().includes('purchase') || stepName.toLowerCase().includes('buy') || stepName.toLowerCase().includes('order')) {
                stageNames.push('Purchase and Onboarding');
            } else {
                // Default meaningful names based on position
                const defaultNames = ['Initial Engagement', 'Active Collaboration', 'Final Achievement', 'Follow-up Actions'];
                stageNames.push(defaultNames[i] || `Stage ${i + 1}`);
            }
        }

        return stageNames;
    }

    createFallbackStory(rawText, formData) {
        // Create a fallback story structure if JSON parsing fails
        const lines = rawText.split('\n').filter(line => line.trim());
        const numScenes = formData.nbSteps || 6;
        
        return {
            story_name: formData.description.substring(0, 50) + ' - Customer Journey',
            persona: {
                name: 'Customer Persona',
                age_range: '25-45',
                occupation: 'Professional',
                personality_traits: ['Goal-oriented', 'Tech-savvy', 'Value-conscious'],
                background: 'Typical customer for this journey'
            },
            story_context: formData.description,
            scenes: Array.from({length: numScenes}, (_, i) => ({
                step_name: `Step ${i + 1}`,
                step_description: lines[i] || `Customer journey step ${i + 1} involving ${formData.description}`,
                scene_description: `Professional scene showing customer in step ${i + 1} of their journey`,
                photo_shooting: 'MEDIUM_SHOT',
                customer_emotion: formData.includeEmotions ? 'Engaged' : undefined,
                pain_points: formData.includeChallenges ? ['Minor friction point'] : [],
                opportunities: ['Improvement opportunity']
            }))
        };
    }

    async generateSceneImages(scenes, formData, story) {
        // Get the same style description used for persona
        const styleDescription = this.getStyleDescription(formData);

        // Create consistent persona description (matching Python version)
        const personaDescription = story && story.persona ?
            `${story.persona.name} is ${story.persona.personality_traits.slice(0, 2).join(', ')}` :
            'Professional person';

        const generatedScenes = [];

        for (let i = 0; i < scenes.length; i++) {
            const scene = scenes[i];

            try {
                // Create enhanced prompt matching Python version
                const stepContext = `Customer goal: ${scene.customer_goal || 'Complete task'}. Step context: ${scene.step_description}`;

                const imagePrompt = this.enhanceImagePrompt(
                    scene.scene_description,
                    styleDescription,
                    personaDescription,
                    scene.photo_shooting || "MEDIUM_SHOT",
                    stepContext
                );

                console.log(`Generating image ${i + 1}/${scenes.length}: ${imagePrompt.substring(0, 100)}...`);

                // Generate image using DALL-E 3 with same settings as persona
                const imageResponse = await this.openai.images.generate({
                    model: 'dall-e-3',
                    prompt: imagePrompt,
                    size: this.getDalleSize(formData.aspectRatio),
                    quality: 'hd', // Use HD quality like persona
                    style: 'natural', // More realistic and professional style
                    n: 1
                });

                const imageUrl = imageResponse.data[0].url;
                
                // Download the image
                const imageData = await this.downloadImage(imageUrl);
                
                generatedScenes.push({
                    ...scene,
                    image_data: imageData,
                    image_prompt: imagePrompt
                });
                
            } catch (error) {
                console.error(`Error generating image for scene ${i + 1}:`, error);
                // Add scene without image
                generatedScenes.push({
                    ...scene,
                    image_data: null,
                    image_prompt: 'Image generation failed'
                });
            }
        }

        return generatedScenes;
    }

    createImagePrompt(scene, styleDescription, formData) {
        let prompt = scene.scene_description;
        
        // Add style information
        if (styleDescription) {
            prompt += `. Style: ${styleDescription}`;
        }
        
        // Add emotional context if enabled
        if (formData.includeEmotions && scene.customer_emotion) {
            prompt += `. Customer emotion: ${scene.customer_emotion}`;
        }
        
        // Add shot type
        const shotTypes = {
            'CLOSEUP_SHOT': 'close-up shot',
            'MEDIUM_CLOSEUP_SHOT': 'medium close-up shot',
            'MEDIUM_SHOT': 'medium shot',
            'AMERICAN_SHOT': 'american shot',
            'FULL_SHOT': 'full shot'
        };
        
        if (scene.photo_shooting && shotTypes[scene.photo_shooting]) {
            prompt += `. Camera: ${shotTypes[scene.photo_shooting]}`;
        }
        
        // Add quality and format preferences
        prompt += '. High quality, professional, clean composition, suitable for business presentation.';
        
        return prompt;
    }

    getDalleSize(aspectRatio) {
        switch (aspectRatio) {
            case '1:1':
            case 'SQUARE':
                return '1024x1024';
            case '16:9':
            case 'WIDESCREEN':
                return '1792x1024';
            case '9:16':
            case 'VERTICAL':
                return '1024x1792';
            default:
                return '1792x1024';
        }
    }

    async downloadImage(url) {
        console.log('Downloading image from:', url.substring(0, 50) + '...');
        try {
            const response = await axios.get(url, { responseType: 'arraybuffer' });
            const buffer = Buffer.from(response.data, 'binary');
            // Convert to base64 string for PptxGenJS
            const base64String = `data:image/png;base64,${buffer.toString('base64')}`;
            console.log('Successfully downloaded and converted image:', buffer.length, 'bytes');
            return base64String;
        } catch (error) {
            console.error('Error downloading image:', error);
            return null;
        }
    }

    getStyleDescription(formData) {
        // Map base styles to descriptions matching Python version
        const styleDescriptions = {
            'sketchy': 'Sketchy graphic style, quick, rough and imprecise marker strokes, not too detailed, with few strokes, white background, black color for lines and a light gray scale for shades',
            'cartoon': 'Cartoon-like images with vibrant colors, clean lines, friendly and approachable',
            'realistic': 'Photo-realistic pictures with high detail, natural lighting, professional photography quality',
            'custom': formData.customStyleDescription || 'Professional, high-quality artistic style'
        };

        let description = styleDescriptions[formData.baseStyle] || styleDescriptions['realistic'];

        // Add mood keywords if available
        if (formData.moodKeywords && formData.moodKeywords.length > 0) {
            description += `, ${formData.moodKeywords.join(', ')} mood`;
        }

        // Add technical modifiers if available
        if (formData.technicalModifiers && formData.technicalModifiers.length > 0) {
            description += `, ${formData.technicalModifiers.join(', ')}`;
        }

        return description;
    }

    enhanceImagePrompt(basePrompt, styleDescription, personaDescription, shotType, stepContext) {
        // Composition and framing instructions matching Python version
        const compositionGuide = {
            "FULL_SHOT": "full body shot showing the entire person and environment, wide angle view",
            "MEDIUM_SHOT": "medium shot from waist up, showing person and immediate surroundings",
            "MEDIUM_CLOSEUP_SHOT": "medium close-up shot from chest up, focusing on person with some background context",
            "CLOSEUP_SHOT": "close-up shot of head and shoulders, intimate and detailed view",
            "EXTREME_CLOSEUP_SHOT": "extreme close-up focusing on facial expressions and emotions"
        };

        const composition = compositionGuide[shotType] || compositionGuide["MEDIUM_SHOT"];

        // Build enhanced prompt
        let enhancedPrompt = `${basePrompt}. ${personaDescription}. ${composition}. ${stepContext}. Style: ${styleDescription}. High quality, professional composition, good lighting, clear details.`;

        // Ensure prompt doesn't exceed DALL-E 3 limit
        if (enhancedPrompt.length > 1000) {
            enhancedPrompt = enhancedPrompt.substring(0, 997) + '...';
        }

        return enhancedPrompt;
    }

    createPresentationTemplate(formData) {
        const pptx = new PptxGenJS();
        
        // Set presentation properties to match Python version exactly
        pptx.defineLayout({ name: 'CUSTOM', width: 14.17, height: 7.09 }); // 36cm x 18cm
        pptx.layout = 'CUSTOM';
        pptx.title = formData.description || 'Customer Journey Storyboard';
        pptx.author = 'AI Storyboard Generator';
        pptx.company = formData.brandConfig?.brand_name || 'Generated Storyboard';
        
        return pptx;
    }

    async generateStoryboard(formData, progressCallback) {
        try {
            progressCallback('Initializing storyboard generation...');
            
            // Step 1: Generate or reuse edited story structure
            let story;
            if (formData.editedPreview && formData.editedPreview.stages) {
                progressCallback('Using edited story preview...');
                story = this.convertPreviewToStory(formData.editedPreview, formData);
            } else {
                progressCallback('Creating story structure with AI...');
                story = await this.generateStoryStructure(formData);
                story = await this.translateStageNamesIfNeeded(story, formData);
            }
            
            // Step 2: Create PowerPoint presentation
            progressCallback('Setting up PowerPoint presentation...');
            const pptx = this.createPresentationTemplate(formData);
            
            // Step 3: Generate persona image first
            progressCallback('Generating persona image...');
            const personaImage = await this.generatePersonaImage(story, formData);
            
            // Step 4: Generate images for each scene
            progressCallback('Generating AI images for scenes...');
            const scenes = await this.generateSceneImages(story.scenes, formData, story);
            
            // Step 5: Build the single comprehensive slide (Python style)
            progressCallback('Building PowerPoint presentation...');
            await this.buildComprehensiveSlide(pptx, story, scenes, personaImage, formData);
            
            // Step 6: Prepare the presentation for download
            progressCallback('Preparing PowerPoint presentation...');
            const { filename, fileData } = await this.preparePresentation(pptx, formData);
            
            progressCallback('Storyboard generation completed!');
            
            return {
                success: true,
                filename: filename,
                fileData: fileData,
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            console.error('Storyboard generation error:', error);
            throw error;
        }
    }

    convertPreviewToStory(preview, formData) {
        // Convert UI preview format into story structure with stages and flat scenes
        const shotMap = {
            'Full shot': 'FULL_SHOT',
            'American shot': 'AMERICAN_SHOT',
            'Medium shot': 'MEDIUM_SHOT',
            'Medium close-up shot': 'MEDIUM_CLOSEUP_SHOT',
            'Close-up shot': 'CLOSEUP_SHOT'
        };

        const persona_profile = {
            name: preview.persona?.name || 'Customer',
            age_range: preview.persona?.age_range || '—',
            occupation: preview.persona?.occupation || '—',
            personality_traits: preview.persona?.traits || []
        };

        const stages = (preview.stages || []).map(stage => ({
            stage_name: stage.name,
            stage_description: stage.description || '',
            stage_goal: stage.goal || '',
            steps: (stage.steps || []).map(step => ({
                step_name: step.name,
                step_description: step.description || '',
                scene_description: step.description || '',
                photo_shooting: shotMap[step.shot_type] || 'MEDIUM_SHOT',
                customer_emotion: step.emotion && step.emotion !== 'N/A' ? step.emotion : undefined,
                pain_points: formData.includeChallenges ? [] : [],
                opportunities: []
            }))
        }));

        // Build a flat scenes list preserving order
        const scenes = [];
        stages.forEach(stage => {
            stage.steps.forEach(step => {
                scenes.push({
                    step_name: step.step_name,
                    step_description: step.step_description,
                    scene_description: step.scene_description,
                    photo_shooting: step.photo_shooting,
                    customer_emotion: step.customer_emotion,
                    pain_points: step.pain_points,
                    opportunities: step.opportunities
                });
            });
        });

        return {
            story_name: preview.story_name || 'Storyboard',
            persona_profile,
            story_context: preview.story_context || '',
            stages,
            scenes
        };
    }

    async generatePersonaImage(story, formData) {
        try {
            // Get the same style description used for scenes
            const styleDescription = this.getStyleDescription(formData);

            // Create consistent persona description (supports persona or persona_profile)
            const persona = story.persona || story.persona_profile || {};
            const personaDescription = `${persona.name || 'Customer'} is ${(persona.personality_traits || []).slice(0, 2).join(', ')}`;

            // Enhanced persona image prompt matching Python version
            const personaPrompt = this.enhanceImagePrompt(
                "Professional headshot portrait in a clean, modern setting",
                styleDescription,
                personaDescription,
                "MEDIUM_CLOSEUP_SHOT",
                "Confident and approachable expression, looking directly at camera"
            );

            console.log('Generating persona image with style:', formData.baseStyle);
            console.log('Persona prompt:', personaPrompt);

            const imageResponse = await this.openai.images.generate({
                model: 'dall-e-3',
                prompt: personaPrompt,
                size: this.getDalleSize(formData.aspectRatio), // Use same aspect ratio as scenes
                quality: 'hd', // Use HD quality like Python version
                style: 'natural', // More realistic and professional style
                n: 1
            });

            const imageUrl = imageResponse.data[0].url;
            return await this.downloadImage(imageUrl);

        } catch (error) {
            console.error('Error generating persona image:', error);
            return null;
        }
    }

    async addLogoToSlide(slide, formData) {
        // Add brand logo if available (consistent across all slides)
        if (formData.brandConfig && formData.brandConfig.logo_data) {
            try {
                console.log('Adding brand logo to slide');

                // Convert logo size from pixels to inches (matching Python conversion)
                const logoWidth = (formData.brandConfig.logo_size[0] / 28.35) / 2.54; // pixels to cm to inches
                const logoHeight = (formData.brandConfig.logo_size[1] / 28.35) / 2.54;

                // Position logo based on brand_config.logo_position (matching Python logic)
                let logoLeft, logoTop;
                const slideWidth = 13.33; // Standard slide width in inches
                const slideHeight = 7.5; // Standard slide height in inches

                switch (formData.brandConfig.logo_position) {
                    case "top-right":
                        logoLeft = slideWidth - logoWidth - 0.39; // Cm(1) converted to inches
                        logoTop = 0.39; // Cm(1) converted to inches
                        break;
                    case "top-left":
                        logoLeft = 0.39; // Cm(1) converted to inches
                        logoTop = 0.39; // Cm(1) converted to inches
                        break;
                    case "bottom-right":
                        logoLeft = slideWidth - logoWidth - 0.39;
                        logoTop = slideHeight - logoHeight - 0.39;
                        break;
                    case "bottom-left":
                    default:
                        logoLeft = 0.39;
                        logoTop = slideHeight - logoHeight - 0.39;
                        break;
                }

                // Ensure logo data is in correct format
                let logoData = formData.brandConfig.logo_data;
                if (logoData && !logoData.startsWith('data:')) {
                    // If it's just base64, add the data URL prefix
                    logoData = `data:image/png;base64,${logoData}`;
                }

                // Add logo image to slide
                slide.addImage({
                    data: logoData, // Base64 data with proper prefix
                    x: logoLeft,
                    y: logoTop,
                    w: logoWidth,
                    h: logoHeight
                });

                console.log(`Logo added at position: ${formData.brandConfig.logo_position} (${logoLeft}, ${logoTop})`);
            } catch (error) {
                console.error('Failed to add logo:', error);
            }
        }
    }

    async buildComprehensiveSlide(pptx, story, scenes, personaImage, formData) {
        // Ensure story has stages structure
        if (!story.stages && story.scenes) {
            story = this.convertScenestoStages(story);
        }

        const slide = pptx.addSlide();
        
        // Set background color (matching Python)
        const bgColor = formData.brandConfig?.background_color ?
            this.rgbArrayToHex(formData.brandConfig.background_color) : 'F8F9FA';
        slide.background = { fill: bgColor };

        // Add brand logo using helper method
        await this.addLogoToSlide(slide, formData);

        // Normalize persona structure (support persona or persona_profile)
        const persona = story.persona || story.persona_profile || {};

        // Title section (matching Python coordinates - converted from cm to inches). Use text color if provided.
        slide.addText(story.story_name, {
            x: 0.59, y: 0.59, w: 6.30, h: 1.18, // Cm(1.5), Cm(1.5), Cm(16), Cm(3)
            fontSize: 26,
            bold: true,
            color: formData.brandConfig?.text_color ?
                this.rgbArrayToHex(formData.brandConfig.text_color) : '2C3E50',
            fontFace: formData.brandConfig?.primary_font || 'Arial',
            valign: 'middle',
            wrap: true
        });
        
        // Persona section background (temporarily disabled)
        // const personaBgColor = 'FFFFFF';
        // const personaBgX = 7.68; // Cm(19.5) converted
        // const personaBgY = 0.47; // Cm(1.2) converted  
        // const personaBgW = 5.91; // Cm(15) converted
        // const personaBgH = 1.50; // Cm(3.8) converted
        
        // slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        //     x: personaBgX, 
        //     y: personaBgY, 
        //     w: personaBgW, 
        //     h: personaBgH,
        //     fill: { color: personaBgColor },
        //     line: { color: 'E1E8ED', width: 1 }
        // });
        
        // Persona text
    const personaText = `${persona.name || ''} | ${persona.age_range || ''} | ${persona.occupation || ''}\n\n${story.story_context || ''}\n\nKey traits: ${(persona.personality_traits || persona.traits || []).slice(0, 3).join(', ')}`;
        
        slide.addText(personaText, {
            x: 9.25, y: 0.59, w: 4.13, h: 1.38, // Cm(23.5), Cm(1.5), Cm(10.5), Cm(3.5)
            fontSize: 10,
            color: '34495E',
            fontFace: 'Arial',
            valign: 'middle',
            wrap: true
        });
        
        // Add persona image if available
        console.log('Persona image data:', !!personaImage, personaImage ? `(${personaImage.length} bytes)` : '(no data)');
    if (personaImage) {
            const aspectRatio = this.aspectRatios[formData.aspectRatio] || this.aspectRatios['16:9'];
            const imgSize = 1.38; // Cm(3.5) converted
            let imgWidth = imgSize * aspectRatio.width;
            let imgHeight = imgSize;
            let imgLeft = 7.87 - (imgWidth - imgSize); // Cm(20) adjusted
            const imgTop = 0.59; // Cm(1.5)
            
            console.log('Adding persona image at position:', {imgLeft, imgTop, imgWidth, imgHeight});
            
            try {
                slide.addImage({
                    data: personaImage,
                    x: imgLeft, y: imgTop, w: imgWidth, h: imgHeight
                    // Removed rounding to keep images rectangular
                });
                console.log('Successfully added persona image');
            } catch (error) {
                console.error('Error adding persona image:', error);
            }
        } else {
            console.log('No persona image to add');
        }
        
        // Main table background (temporarily disabled)
        // const tableHeight = 4.33; // Calculated based on Python logic
        // slide.addShape(pptx.shapes.ROUNDED_RECTANGLE, {
        //     x: 0.39, 
        //     y: 2.36, 
        //     w: 13.39, 
        //     h: tableHeight + 0.39, // Cm(1), Cm(6), Cm(34), table_height + Cm(1)
        //     fill: { color: formData.brandConfig?.primary_color ? 
        //         this.rgbArrayToHex(formData.brandConfig.primary_color) : '2C3E50' }
        // });
        
        // Create the comprehensive table
        const tableHeight = 4.33; // Still needed for table dimensions
        await this.createComprehensiveTable(slide, story, scenes, formData);
    }

    async createComprehensiveTable(slide, story, scenes, formData) {
        // Calculate total columns based on all steps across all stages
        let totalCols = (story.stages || []).reduce((total, stage) => total + (stage.steps ? stage.steps.length : 0), 0);
        if (!totalCols || totalCols < 1) totalCols = 1;
        const rows = 4;

        // Helpers for unit conversion (cm -> inches)
        const cmToIn = (cm) => cm * 0.393701;

        // Calculate dimensions (matching Streamlit)
        const aspectRatio = this.aspectRatios[formData.aspectRatio] || this.aspectRatios['16:9'];
        const tableWidthCm = 33.0; // width used for table (cm)
        const tableXIn = cmToIn(1.5);
        const tableYIn = cmToIn(6);
        const colWidthCm = tableWidthCm / totalCols;
        const imgCellMaxHeightCm = 6.0;
        const imgHeightCm = Math.min((colWidthCm / aspectRatio.width), imgCellMaxHeightCm);
        const stageRowCm = 0.8;
        const stepRowCm = 1.2;
        const descRowCm = 4.0;
        const tableHeightCm = stageRowCm + stepRowCm + imgHeightCm + descRowCm;

        // Build table data structure matching Python logic
        const tableData = [];

        // Row 0: Stage headers (grouped by stage)
        const stageHeaders = [];
        for (const stage of story.stages || []) {
            const span = (stage.steps || []).length || 1;
            const baseName = stage.stage_name || '';
            // Dynamically shrink font for very large spans
            let fontSize = 11;
            if (span >= 5) fontSize = 8;
            else if (span >= 3) fontSize = 9;
            stageHeaders.push({
                text: baseName, // keep translated casing
                options: {
                    fill: formData.brandConfig?.primary_color ? this.rgbArrayToHex(formData.brandConfig.primary_color) : '2C3E50',
                    color: 'FFFFFF',
                    fontSize,
                    bold: true,
                    align: 'center',
                    valign: 'middle',
                    colSpan: span
                }
            });
            for (let i = 1; i < span; i++) stageHeaders.push({ text: '', options: { fill: this.rgbArrayToHex(formData.brandConfig?.primary_color || [44,62,80]) } });
        }
        tableData.push(stageHeaders);

        // Row 1: Step names
        const stepNames = [];
        for (const stage of story.stages || []) {
            for (const step of (stage.steps || [])) {
                stepNames.push({
                    text: step.step_name,
                    options: {
                        fill: formData.brandConfig?.secondary_color ? this.rgbArrayToHex(formData.brandConfig.secondary_color) : '34495E',
                        color: 'FFFFFF',
                        fontSize: 10,
                        bold: true,
                        align: 'center',
                        valign: 'middle'
                    }
                });
            }
        }
        tableData.push(stepNames);

        // Row 2: Images placeholders (images added later)
        const imageRow = [];
        for (const stage of story.stages || []) {
            for (const step of (stage.steps || [])) {
                const scene = scenes.find(s => s.step_name === step.step_name);
                imageRow.push({
                    text: (scene && scene.image_data) ? '' : '[Image]',
                    options: {
                        fill: formData.brandConfig?.primary_color ? this.rgbArrayToHex(formData.brandConfig.primary_color) : '2C3E50',
                        color: (scene && scene.image_data) ? '2C3E50' : '95A5A6',
                        align: 'center',
                        valign: 'middle'
                    }
                });
            }
        }
        tableData.push(imageRow);

        // Row 3: Descriptions
        const descriptions = [];
        for (const stage of story.stages || []) {
            for (const step of (stage.steps || [])) {
                const scene = scenes.find(s => s.step_name === step.step_name);
                let description = step.step_description || '';
                if (scene) {
                    if (formData.includeEmotions && scene.customer_emotion) description += `\n\nEmotion: ${scene.customer_emotion}`;
                    if (formData.includeChallenges && scene.pain_points && scene.pain_points.length > 0) description += `\n\nChallenges: ${scene.pain_points.slice(0, 2).join(', ')}`;
                }
                descriptions.push({
                    text: description,
                    options: { fill: 'F8F9FA', color: '2C3E50', fontSize: 9, align: 'left', valign: 'top' }
                });
            }
        }
        tableData.push(descriptions);

        // Add the table with dynamic row heights
        slide.addTable(tableData, {
            x: tableXIn,
            y: tableYIn,
            w: cmToIn(tableWidthCm),
            h: cmToIn(tableHeightCm),
            border: { pt: 1, color: '2C3E50' },
            fontSize: 10,
            fontFace: 'Arial',
            rowH: [ cmToIn(stageRowCm), cmToIn(stepRowCm), cmToIn(imgHeightCm), cmToIn(descRowCm) ]
        });

        // Add images to table cells separately (PptxGenJS limitation workaround)
        await this.addImagesToTable(slide, story, scenes, totalCols, formData, { tableXIn, tableYIn, tableWidthCm, stageRowCm, stepRowCm, imgHeightCm });
    }

    async addImagesToTable(slide, story, scenes, totalCols, formData, dims) {
        console.log(`Adding images to table: ${scenes.length} scenes, ${totalCols} columns`);
        const cmToIn = (cm) => cm * 0.393701;
        const tableX = dims?.tableXIn ?? cmToIn(1.5);
        const tableY = dims?.tableYIn ?? cmToIn(6);
        const tableWidth = cmToIn(dims?.tableWidthCm ?? 33.0);
        const stageRowIn = cmToIn(dims?.stageRowCm ?? 0.8);
        const stepRowIn = cmToIn(dims?.stepRowCm ?? 1.2);
        const imageRowHeightIn = cmToIn(dims?.imgHeightCm ?? 6.0);
        const cellWidth = tableWidth / totalCols;
        const imageRowY = tableY + stageRowIn + stepRowIn;

        const aspectRatio = this.aspectRatios[formData.aspectRatio] || this.aspectRatios['16:9'];

        // Iterate through stages and steps to match the table structure
        let colIndex = 0;
        for (const stage of story.stages || []) {
            for (const step of (stage.steps || [])) {
                const scene = scenes.find(s => s.step_name === step.step_name);
                if (scene && scene.image_data) {
                    try {
                        const cellX = tableX + (colIndex * cellWidth);
                        const maxImgWidth = imageRowHeightIn * aspectRatio.width;
                        const imgWidth = Math.min(cellWidth * 0.9, maxImgWidth);
                        const imgHeight = imgWidth / aspectRatio.width;
                        const imgX = cellX + (cellWidth - imgWidth) / 2;
                        const imgY = imageRowY + (imageRowHeightIn - imgHeight) / 2;
                        slide.addImage({ data: scene.image_data, x: imgX, y: imgY, w: imgWidth, h: imgHeight });
                    } catch (error) {
                        console.error(`Error adding image for step "${step.step_name}":`, error);
                    }
                }
                colIndex++;
            }
        }
    }

    async createTitleSlide(pptx, story, formData) {
        const slide = pptx.addSlide();

        // Background color
        slide.background = { color: formData.brandConfig?.background_color ?
            this.rgbArrayToHex(formData.brandConfig.background_color) : 'F8F9FA' };

        // Add logo
        await this.addLogoToSlide(slide, formData);

        // Title
        slide.addText(story.story_name, {
            x: 1, y: 2, w: 8, h: 2,
            fontSize: 36,
            bold: true,
            color: formData.brandConfig?.primary_color ?
                this.rgbArrayToHex(formData.brandConfig.primary_color) : '2C3E50',
            align: 'center'
        });

        // Subtitle
        slide.addText('AI-Generated Customer Journey Storyboard', {
            x: 1, y: 4, w: 8, h: 1,
            fontSize: 20,
            color: formData.brandConfig?.text_color ?
                this.rgbArrayToHex(formData.brandConfig.text_color) : '7F8C8D',
            align: 'center'
        });

        // Date
        slide.addText(`Generated on ${new Date().toLocaleDateString()}`, {
            x: 1, y: 6, w: 8, h: 0.5,
            fontSize: 14,
            color: '95A5A6',
            align: 'center'
        });
    }

    createPersonaSlide(pptx, story, formData) {
        const slide = pptx.addSlide();
        
        // Background
        slide.background = { color: formData.brandConfig?.background_color ? 
            this.rgbArrayToHex(formData.brandConfig.background_color) : 'F8F9FA' };
        
        // Title
        slide.addText('Customer Persona', {
            x: 1, y: 0.5, w: 8, h: 1,
            fontSize: 28,
            bold: true,
            color: formData.brandConfig?.primary_color ? 
                this.rgbArrayToHex(formData.brandConfig.primary_color) : '2C3E50'
        });
        
        // Persona details
        const personaText = `Name: ${story.persona.name}
Age: ${story.persona.age_range}
Occupation: ${story.persona.occupation}

Background: ${story.persona.background}

Key Traits:
• ${story.persona.personality_traits.join('\n• ')}

Journey Context:
${story.story_context}`;
        
        slide.addText(personaText, {
            x: 1, y: 1.5, w: 7, h: 5,
            fontSize: 16,
            color: formData.brandConfig?.text_color ? 
                this.rgbArrayToHex(formData.brandConfig.text_color) : '2C3E50',
            valign: 'top'
        });
    }

    createJourneyOverviewSlide(pptx, story, scenes, formData) {
        const slide = pptx.addSlide();
        
        // Background
        slide.background = { color: formData.brandConfig?.background_color ? 
            this.rgbArrayToHex(formData.brandConfig.background_color) : 'F8F9FA' };
        
        // Title
        slide.addText('Journey Overview', {
            x: 1, y: 0.5, w: 8, h: 1,
            fontSize: 28,
            bold: true,
            color: formData.brandConfig?.primary_color ? 
                this.rgbArrayToHex(formData.brandConfig.primary_color) : '2C3E50'
        });
        
        // Journey steps
        const stepsText = scenes.map((scene, index) => 
            `${index + 1}. ${scene.step_name}: ${scene.step_description.substring(0, 100)}${scene.step_description.length > 100 ? '...' : ''}`
        ).join('\n\n');
        
        slide.addText(stepsText, {
            x: 1, y: 1.5, w: 8, h: 5,
            fontSize: 14,
            color: formData.brandConfig?.text_color ? 
                this.rgbArrayToHex(formData.brandConfig.text_color) : '2C3E50',
            valign: 'top'
        });
    }

    createSceneSlide(pptx, scene, sceneNumber, formData) {
        const slide = pptx.addSlide();
        
        // Background
        slide.background = { color: formData.brandConfig?.background_color ? 
            this.rgbArrayToHex(formData.brandConfig.background_color) : 'F8F9FA' };
        
        // Scene number and title
        slide.addText(`Step ${sceneNumber}: ${scene.step_name}`, {
            x: 0.5, y: 0.3, w: 9, h: 0.8,
            fontSize: 24,
            bold: true,
            color: formData.brandConfig?.primary_color ? 
                this.rgbArrayToHex(formData.brandConfig.primary_color) : '2C3E50'
        });
        
        // Add image if available
        if (scene.image_data) {
            try {
                slide.addImage({
                    data: scene.image_data,
                    x: 0.5, y: 1.3, w: 4.5, h: 3.5
                    // Removed rounding to keep images rectangular
                });
            } catch (error) {
                console.error(`Error adding image for scene ${sceneNumber}:`, error);
                // Add placeholder text instead
                slide.addText('[Image Generation Failed]', {
                    x: 0.5, y: 1.3, w: 4.5, h: 3.5,
                    fontSize: 16,
                    color: '95A5A6',
                    align: 'center',
                    valign: 'middle',
                    fill: { color: 'EEEEEE' }
                });
            }
        } else {
            // Add placeholder
            slide.addText('[Image Not Available]', {
                x: 0.5, y: 1.3, w: 4.5, h: 3.5,
                fontSize: 16,
                color: '95A5A6',
                align: 'center',
                valign: 'middle',
                fill: { color: 'EEEEEE' }
            });
        }
        
        // Scene description
        let descriptionText = scene.step_description;
        
        if (formData.includeEmotions && scene.customer_emotion) {
            descriptionText += `\n\nCustomer Emotion: ${scene.customer_emotion}`;
        }
        
        if (formData.includeChallenges && scene.pain_points && scene.pain_points.length > 0) {
            descriptionText += `\n\nChallenges:\n• ${scene.pain_points.join('\n• ')}`;
        }
        
        if (scene.opportunities && scene.opportunities.length > 0) {
            descriptionText += `\n\nOpportunities:\n• ${scene.opportunities.join('\n• ')}`;
        }
        
        slide.addText(descriptionText, {
            x: 5.2, y: 1.3, w: 4.3, h: 4.5,
            fontSize: 14,
            color: formData.brandConfig?.text_color ? 
                this.rgbArrayToHex(formData.brandConfig.text_color) : '2C3E50',
            valign: 'top'
        });
    }

    createSummarySlide(pptx, story, scenes, formData) {
        const slide = pptx.addSlide();
        
        // Background
        slide.background = { color: formData.brandConfig?.background_color ? 
            this.rgbArrayToHex(formData.brandConfig.background_color) : 'F8F9FA' };
        
        // Title
        slide.addText('Journey Summary & Next Steps', {
            x: 1, y: 0.5, w: 8, h: 1,
            fontSize: 28,
            bold: true,
            color: formData.brandConfig?.primary_color ? 
                this.rgbArrayToHex(formData.brandConfig.primary_color) : '2C3E50'
        });
        
        // Summary content
        const keyOpportunities = [];
        scenes.forEach(scene => {
            if (scene.opportunities) {
                keyOpportunities.push(...scene.opportunities);
            }
        });
        
        const summaryText = `Key Journey Insights:

• ${scenes.length} critical touchpoints in the customer journey
• Persona: ${story.persona.name} (${story.persona.age_range}, ${story.persona.occupation})
${formData.includeEmotions ? `• Emotional journey mapped across all steps` : ''}
${formData.includeChallenges ? `• Pain points identified and documented` : ''}

Top Opportunities for Improvement:
${keyOpportunities.slice(0, 5).map(opp => `• ${opp}`).join('\n')}

Next Steps:
• Review and validate journey with stakeholders
• Prioritize improvement opportunities
• Develop implementation roadmap
• Set up measurement and tracking`;
        
        slide.addText(summaryText, {
            x: 1, y: 1.5, w: 8, h: 5,
            fontSize: 16,
            color: formData.brandConfig?.text_color ? 
                this.rgbArrayToHex(formData.brandConfig.text_color) : '2C3E50',
            valign: 'top'
        });
    }

    async buildPresentation(pptx, story, scenes, formData) {
        // Create title slide
        await this.createTitleSlide(pptx, story, formData);

        // Create comprehensive table slide
        await this.createComprehensiveTable(pptx.addSlide(), story, scenes, formData);

        // Create individual scene slides (matching Python structure)
        let sceneNumber = 1;
        for (const stage of story.stages) {
            for (const step of stage.steps) {
                // Find the corresponding scene for this step
                const scene = scenes.find(s => s.step_name === step.step_name);
                if (scene) {
                    this.createSceneSlide(pptx, scene, sceneNumber, formData);
                    sceneNumber++;
                }
            }
        }

        // Create journey overview/summary slide
        this.createJourneyOverviewSlide(pptx, story, scenes, formData);
    }

    async preparePresentation(pptx, formData) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0] + '_' + 
                         new Date().toTimeString().split(' ')[0].replace(/:/g, '');
        const projectName = (formData.description || 'storyboard').replace(/[^a-z0-9]/gi, '_').substring(0, 30);
        const filename = `storyboard_${timestamp}.pptx`;
        
        // Generate the presentation data
        const fileData = await pptx.write('arraybuffer');
        
        return {
            filename: filename,
            fileData: fileData
        };
    }

    rgbArrayToHex(rgbArray) {
        if (!Array.isArray(rgbArray) || rgbArray.length !== 3) return '000000';
        return rgbArray.map(c => Math.max(0, Math.min(255, c)).toString(16).padStart(2, '0')).join('').toUpperCase();
    }
}

module.exports = StoryboardGenerator;
